package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 照片上传下载系统主应用程序
 * 
 * <p>这是一个基于Spring Boot 3.2.0的照片管理系统，提供以下核心功能：</p>
 * <ul>
 *   <li>照片上传功能（支持文件格式校验、大小限制）</li>
 *   <li>照片下载与预览功能</li>
 *   <li>照片列表展示与删除</li>
 *   <li>基于Spring Security的安全控制</li>
 * </ul>
 * 
 * <p>技术架构：</p>
 * <ul>
 *   <li>Spring Boot 3.2.0 - 核心框架</li>
 *   <li>Spring Data JPA - 数据访问层</li>
 *   <li>H2 Database - 内存数据库</li>
 *   <li>Spring Security - 安全认证</li>
 *   <li>imgscalr-lib - 图片处理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
public class PhotoUploadDownloadApplication {

    /**
     * 应用程序主入口
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(PhotoUploadDownloadApplication.class, args);
    }
}
